
import requests
import urllib.parse
import json
from playwright.sync_api import sync_playwright
import time
import random
import string

base_url = 'https://1112244.xyz/api/v1'

class Baipiao:
    def __init__(self):
        self.session = requests.session()
        self.browser = None
        self.page = None
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'cache-control': 'no-cache',
            'content-language': 'zh-CN',
            'content-type': 'application/x-www-form-urlencoded',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'Referer': 'https://1112244.xyz/'
        }
    
    def init_browser(self):
        """初始化浏览器"""
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.launch(
            headless=False,  # 显示浏览器
            args=[
                '--disable-blink-features=AutomationControlled',
                '--ignore-certificate-errors',
                '--ignore-ssl-errors',
                '--disable-web-security'
            ]
        )
        self.page = self.browser.new_page()
        
        # 隐藏webdriver特征
        self.page.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)
    
    def get_turnstile_token(self, email, password, invite_code):
        """获取Turnstile验证码token"""
        if not self.browser:
            self.init_browser()
        
        try:
            # 访问注册页面
            self.page.goto('https://1112244.xyz/#/register')
            
            # 等待页面加载完成
            self.page.wait_for_load_state('networkidle')
            time.sleep(3)
            
            print("开始填写注册表单...")

            # 填写邮箱
            print("正在填写邮箱...")
            email_input = self.page.locator('input[type="email"]').first
            email_input.wait_for(timeout=10000)
            email_input.fill(email)
            print(f"已填写邮箱: {email}")

            time.sleep(1)

            # 填写密码
            print("正在填写密码...")
            password_input = self.page.locator('input[type="password"]').first
            password_input.wait_for(timeout=10000)
            password_input.fill(password)
            print("已填写密码")

            time.sleep(1)

            # 填写邀请码（如果有）
            if invite_code:
                print("正在填写邀请码...")
                invite_input = self.page.locator('input[placeholder*="邀请码"]')
                if invite_input.count() > 0:
                    invite_input.fill(invite_code)
                    print(f"已填写邀请码: {invite_code}")

            # 点击注册按钮
            print("正在查找注册按钮...")
            register_btn = self.page.locator('button:has-text("注册")').first
            register_btn.wait_for(timeout=10000)
            register_btn.click()
            print("已点击注册按钮")
            
            # 等待Turnstile iframe加载
            print("等待验证码加载...")
            iframe_locator = self.page.locator("iframe[src*='turnstile']")
            iframe_locator.wait_for(timeout=30000)
            
            # 切换到iframe并点击验证码
            iframe = iframe_locator.first
            frame = iframe.content_frame()
            
            # 点击验证码复选框
            checkbox = frame.locator("input[type='checkbox']")
            checkbox.click()
            print("已点击验证码")

            # 等待注册完成
            print("等待注册完成...")
            time.sleep(10)

            # 检查是否注册成功
            try:
                success_message = self.page.locator('text=注册成功').count()
                if success_message > 0:
                    print("检测到注册成功消息")
                else:
                    print("未检测到成功消息，可能需要更长等待时间")
            except:
                pass
            
            # 获取token
            token = self.page.evaluate("""
                () => {
                    const input = document.querySelector('input[name="cf-turnstile-response"]');
                    return input ? input.value : null;
                }
            """)
            
            print(f"获取到token: {token[:50] if token else 'None'}...")
            return token
        
        except Exception as e:
            print(f"获取Turnstile token失败: {e}")
            return None
    
    def auto_register(self, email, password, invite_code):
        """自动注册（包含验证码处理）"""
        token = self.get_turnstile_token(email, password, invite_code)
        if not token:
            print("无法获取验证码token")
            return None
        
        print(f"获取到token: {token[:50]}...")
        
        # 使用获取到的token进行注册
        return self.register(email, password, invite_code, token)
    
    def register(self, email, password, invite_code, turnstile_token, email_code=''):
        data = {
            'email': email,
            'password': password,
            'invite_code': invite_code,
            'email_code': email_code,
            'turnstile_token': turnstile_token
        }
        
        response = self.session.post(
            base_url + "/passport/auth/register",
            headers=self.headers,
            data=data
        )
        
        try:
            result = response.json()
            status = result.get('status')
            message = result.get('message', '')
            
            print(f"状态: {status}")
            print(f"消息: {message}")
            
            if status == 'success':
                print("注册成功！")
            else:
                print("注册失败")
                
        except Exception as e:
            print(f"解析响应失败: {e}")
            print(f"原始响应: {response.text}")
        
        return response
    
    def close_browser(self):
        """关闭浏览器"""
        if self.browser:
            self.browser.close()
        if hasattr(self, 'playwright'):
            self.playwright.stop()

    def generate_random_email(self):
        """生成随机邮箱用户名（不包含@后缀）"""
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        return username

    def generate_random_password(self, length=12):
        """生成随机密码"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choices(chars, k=length))

if __name__ == '__main__':
    baipiao = Baipiao()
    try:
        # 生成随机邮箱和密码
        email = baipiao.generate_random_email()
        password = baipiao.generate_random_password()
        
        print(f"使用邮箱: {email}")
        print(f"使用密码: {password}")
        
        # 自动注册
        baipiao.auto_register(
            email=email,
            password=password,
            invite_code=''
        )
    finally:
        baipiao.close_browser()

import requests
from typing import Dict, Callable, Any, Optional
from abc import ABC, abstractmethod
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProtocolHandler(ABC):
    """协议处理器抽象基类"""

    @abstractmethod
    def handle(self, data: Any) -> Any:
        """处理协议数据"""
        pass

    @abstractmethod
    def validate(self, data: Any) -> bool:
        """验证协议数据格式"""
        pass


class ProtocolRegistry:
    """协议注册器"""

    def __init__(self):
        self._protocols: Dict[str, ProtocolHandler] = {}
        self._middleware: Dict[str, list] = {}

    def register(self, protocol_name: str, handler: ProtocolH<PERSON>ler) -> None:
        """
        注册协议处理器

        Args:
            protocol_name: 协议名称
            handler: 协议处理器实例
        """
        if not isinstance(handler, ProtocolHandler):
            raise TypeError("Handler must be an instance of ProtocolHandler")

        self._protocols[protocol_name] = handler
        self._middleware[protocol_name] = []
        logger.info(f"Protocol '{protocol_name}' registered successfully")

    def unregister(self, protocol_name: str) -> bool:
        """
        注销协议处理器

        Args:
            protocol_name: 协议名称

        Returns:
            bool: 是否成功注销
        """
        if protocol_name in self._protocols:
            del self._protocols[protocol_name]
            del self._middleware[protocol_name]
            logger.info(f"Protocol '{protocol_name}' unregistered successfully")
            return True
        return False

    def get_handler(self, protocol_name: str) -> Optional[ProtocolHandler]:
        """
        获取协议处理器

        Args:
            protocol_name: 协议名称

        Returns:
            ProtocolHandler: 协议处理器实例，如果不存在则返回None
        """
        return self._protocols.get(protocol_name)

    def add_middleware(self, protocol_name: str, middleware: Callable) -> None:
        """
        为协议添加中间件

        Args:
            protocol_name: 协议名称
            middleware: 中间件函数
        """
        if protocol_name not in self._protocols:
            raise ValueError(f"Protocol '{protocol_name}' not registered")

        self._middleware[protocol_name].append(middleware)
        logger.info(f"Middleware added to protocol '{protocol_name}'")

    def process(self, protocol_name: str, data: Any) -> Any:
        """
        处理协议数据

        Args:
            protocol_name: 协议名称
            data: 要处理的数据

        Returns:
            Any: 处理后的数据

        Raises:
            ValueError: 协议未注册或数据验证失败
        """
        handler = self.get_handler(protocol_name)
        if not handler:
            raise ValueError(f"Protocol '{protocol_name}' not registered")

        # 验证数据
        if not handler.validate(data):
            raise ValueError(f"Invalid data format for protocol '{protocol_name}'")

        # 执行中间件
        processed_data = data
        for middleware in self._middleware[protocol_name]:
            processed_data = middleware(processed_data)

        # 处理数据
        result = handler.handle(processed_data)
        logger.info(f"Protocol '{protocol_name}' processed data successfully")
        return result

    def list_protocols(self) -> list:
        """
        列出所有已注册的协议

        Returns:
            list: 协议名称列表
        """
        return list(self._protocols.keys())

    def is_registered(self, protocol_name: str) -> bool:
        """
        检查协议是否已注册

        Args:
            protocol_name: 协议名称

        Returns:
            bool: 是否已注册
        """
        return protocol_name in self._protocols


# 示例协议处理器
class HTTPProtocolHandler(ProtocolHandler):
    """HTTP协议处理器"""

    def validate(self, data: Any) -> bool:
        """验证HTTP请求数据"""
        if not isinstance(data, dict):
            return False
        required_fields = ['url', 'method']
        return all(field in data for field in required_fields)

    def handle(self, data: Any) -> Any:
        """处理HTTP请求"""
        url = data['url']
        method = data.get('method', 'GET').upper()
        headers = data.get('headers', {})
        params = data.get('params', {})
        json_data = data.get('json')

        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, params=params)
            elif method == 'POST':
                response = requests.post(url, headers=headers, params=params, json=json_data)
            elif method == 'PUT':
                response = requests.put(url, headers=headers, params=params, json=json_data)
            elif method == 'DELETE':
                response = requests.delete(url, headers=headers, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            return {
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'content': response.text,
                'json': response.json() if response.headers.get('content-type', '').startswith('application/json') else None
            }
        except Exception as e:
            logger.error(f"HTTP request failed: {e}")
            raise


class WebSocketProtocolHandler(ProtocolHandler):
    """WebSocket协议处理器示例"""

    def validate(self, data: Any) -> bool:
        """验证WebSocket数据"""
        if not isinstance(data, dict):
            return False
        return 'url' in data and 'message' in data

    def handle(self, data: Any) -> Any:
        """处理WebSocket消息"""
        # 这里只是示例，实际需要websocket库
        logger.info(f"WebSocket message to {data['url']}: {data['message']}")
        return {"status": "sent", "message": data['message']}


# 全局协议注册器实例
protocol_registry = ProtocolRegistry()

# 注册默认协议
protocol_registry.register('http', HTTPProtocolHandler())
protocol_registry.register('websocket', WebSocketProtocolHandler())


def register_protocol(name: str, handler: ProtocolHandler) -> None:
    """便捷函数：注册协议"""
    protocol_registry.register(name, handler)


def process_protocol(name: str, data: Any) -> Any:
    """便捷函数：处理协议数据"""
    return protocol_registry.process(name, data)


# 自定义协议处理器示例
class FTPProtocolHandler(ProtocolHandler):
    """FTP协议处理器示例"""

    def validate(self, data: Any) -> bool:
        """验证FTP数据"""
        if not isinstance(data, dict):
            return False
        required_fields = ['host', 'action']
        return all(field in data for field in required_fields)

    def handle(self, data: Any) -> Any:
        """处理FTP操作"""
        host = data['host']
        action = data['action']
        username = data.get('username', 'anonymous')
        password = data.get('password', '')

        # 这里只是模拟FTP操作
        logger.info(f"FTP {action} operation on {host} with user {username}")

        if action == 'list':
            return {"status": "success", "files": ["file1.txt", "file2.txt", "dir1/"]}
        elif action == 'upload':
            filename = data.get('filename', 'unknown')
            return {"status": "uploaded", "filename": filename}
        elif action == 'download':
            filename = data.get('filename', 'unknown')
            return {"status": "downloaded", "filename": filename}
        else:
            raise ValueError(f"Unsupported FTP action: {action}")


class DatabaseProtocolHandler(ProtocolHandler):
    """数据库协议处理器示例"""

    def validate(self, data: Any) -> bool:
        """验证数据库操作数据"""
        if not isinstance(data, dict):
            return False
        return 'query' in data and 'db_type' in data

    def handle(self, data: Any) -> Any:
        """处理数据库操作"""
        query = data['query']
        db_type = data['db_type']
        connection_string = data.get('connection_string', 'localhost')

        logger.info(f"Executing {db_type} query: {query[:50]}...")

        # 模拟数据库操作
        if query.upper().startswith('SELECT'):
            return {
                "status": "success",
                "rows": [
                    {"id": 1, "name": "Alice", "age": 30},
                    {"id": 2, "name": "Bob", "age": 25}
                ],
                "count": 2
            }
        elif query.upper().startswith(('INSERT', 'UPDATE', 'DELETE')):
            return {"status": "success", "affected_rows": 1}
        else:
            return {"status": "executed", "message": "Query executed successfully"}


# 装饰器用于简化协议注册
def protocol_handler(name: str):
    """装饰器：自动注册协议处理器"""
    def decorator(cls):
        if not issubclass(cls, ProtocolHandler):
            raise TypeError("Decorated class must inherit from ProtocolHandler")

        instance = cls()
        protocol_registry.register(name, instance)
        return cls
    return decorator


# 使用装饰器注册协议
@protocol_handler('custom_api')
class CustomAPIHandler(ProtocolHandler):
    """自定义API协议处理器"""

    def validate(self, data: Any) -> bool:
        return isinstance(data, dict) and 'endpoint' in data

    def handle(self, data: Any) -> Any:
        endpoint = data['endpoint']
        payload = data.get('payload', {})

        logger.info(f"Processing custom API call to {endpoint}")
        return {
            "status": "processed",
            "endpoint": endpoint,
            "response": f"Custom response for {endpoint}",
            "payload_received": payload
        }


# 中间件示例
def logging_middleware(data: Any) -> Any:
    """日志中间件"""
    logger.info(f"Processing data: {str(data)[:100]}...")
    return data


def validation_middleware(data: Any) -> Any:
    """验证中间件"""
    if isinstance(data, dict) and 'timestamp' not in data:
        import time
        data['timestamp'] = time.time()
    return data


def rate_limit_middleware(data: Any) -> Any:
    """限流中间件（简单示例）"""
    import time
    # 简单的限流逻辑
    if not hasattr(rate_limit_middleware, 'last_call'):
        rate_limit_middleware.last_call = 0

    current_time = time.time()
    if current_time - rate_limit_middleware.last_call < 1:  # 1秒限制
        logger.warning("Rate limit exceeded, waiting...")
        time.sleep(1)

    rate_limit_middleware.last_call = current_time
    return data


# 批量处理功能
class BatchProcessor:
    """批量协议处理器"""

    def __init__(self, registry: ProtocolRegistry):
        self.registry = registry

    def process_batch(self, requests: list) -> list:
        """
        批量处理协议请求

        Args:
            requests: 请求列表，每个请求包含protocol_name和data

        Returns:
            list: 处理结果列表
        """
        results = []
        for i, request in enumerate(requests):
            try:
                protocol_name = request['protocol']
                data = request['data']
                result = self.registry.process(protocol_name, data)
                results.append({
                    'index': i,
                    'status': 'success',
                    'result': result
                })
            except Exception as e:
                results.append({
                    'index': i,
                    'status': 'error',
                    'error': str(e)
                })
                logger.error(f"Batch processing error at index {i}: {e}")

        return results


# 注册更多协议
protocol_registry.register('ftp', FTPProtocolHandler())
protocol_registry.register('database', DatabaseProtocolHandler())

# 添加中间件到HTTP协议
protocol_registry.add_middleware('http', logging_middleware)
protocol_registry.add_middleware('http', validation_middleware)

# 创建批量处理器
batch_processor = BatchProcessor(protocol_registry)


# 使用示例和测试
if __name__ == "__main__":
    print("=== 协议注册系统测试 ===\n")

    # 1. HTTP协议测试
    print("1. HTTP协议测试:")
    http_data = {
        'url': 'https://httpbin.org/get',
        'method': 'GET',
        'params': {'test': 'value'}
    }

    try:
        result = process_protocol('http', http_data)
        print(f"   HTTP Response Status: {result['status_code']}")
    except Exception as e:
        print(f"   HTTP Error: {e}")

    # 2. FTP协议测试
    print("\n2. FTP协议测试:")
    ftp_data = {
        'host': 'ftp.example.com',
        'action': 'list',
        'username': 'testuser'
    }

    try:
        result = process_protocol('ftp', ftp_data)
        print(f"   FTP Result: {result}")
    except Exception as e:
        print(f"   FTP Error: {e}")

    # 3. 数据库协议测试
    print("\n3. 数据库协议测试:")
    db_data = {
        'query': 'SELECT * FROM users',
        'db_type': 'mysql',
        'connection_string': 'mysql://localhost:3306/testdb'
    }

    try:
        result = process_protocol('database', db_data)
        print(f"   Database Result: {result['count']} rows returned")
    except Exception as e:
        print(f"   Database Error: {e}")

    # 4. 自定义API协议测试
    print("\n4. 自定义API协议测试:")
    api_data = {
        'endpoint': '/api/v1/users',
        'payload': {'name': 'John', 'age': 30}
    }

    try:
        result = process_protocol('custom_api', api_data)
        print(f"   API Result: {result['status']}")
    except Exception as e:
        print(f"   API Error: {e}")

    # 5. 批量处理测试
    print("\n5. 批量处理测试:")
    batch_requests = [
        {'protocol': 'ftp', 'data': {'host': 'ftp1.com', 'action': 'list'}},
        {'protocol': 'custom_api', 'data': {'endpoint': '/test'}},
        {'protocol': 'database', 'data': {'query': 'SELECT COUNT(*) FROM users', 'db_type': 'postgresql'}}
    ]

    batch_results = batch_processor.process_batch(batch_requests)
    successful = sum(1 for r in batch_results if r['status'] == 'success')
    print(f"   Batch processing: {successful}/{len(batch_requests)} successful")

    # 6. 列出所有协议
    print(f"\n6. 已注册的协议: {protocol_registry.list_protocols()}")

    print("\n=== 测试完成 ===")
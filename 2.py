import requests
from typing import Dict, Callable, Any, Optional
from abc import ABC, abstractmethod
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProtocolHandler(ABC):
    """协议处理器抽象基类"""

    @abstractmethod
    def handle(self, data: Any) -> Any:
        """处理协议数据"""
        pass

    @abstractmethod
    def validate(self, data: Any) -> bool:
        """验证协议数据格式"""
        pass


class ProtocolRegistry:
    """协议注册器"""

    def __init__(self):
        self._protocols: Dict[str, ProtocolHandler] = {}
        self._middleware: Dict[str, list] = {}

    def register(self, protocol_name: str, handler: ProtocolH<PERSON>ler) -> None:
        """
        注册协议处理器

        Args:
            protocol_name: 协议名称
            handler: 协议处理器实例
        """
        if not isinstance(handler, ProtocolHandler):
            raise TypeError("Handler must be an instance of ProtocolHandler")

        self._protocols[protocol_name] = handler
        self._middleware[protocol_name] = []
        logger.info(f"Protocol '{protocol_name}' registered successfully")

    def unregister(self, protocol_name: str) -> bool:
        """
        注销协议处理器

        Args:
            protocol_name: 协议名称

        Returns:
            bool: 是否成功注销
        """
        if protocol_name in self._protocols:
            del self._protocols[protocol_name]
            del self._middleware[protocol_name]
            logger.info(f"Protocol '{protocol_name}' unregistered successfully")
            return True
        return False

    def get_handler(self, protocol_name: str) -> Optional[ProtocolHandler]:
        """
        获取协议处理器

        Args:
            protocol_name: 协议名称

        Returns:
            ProtocolHandler: 协议处理器实例，如果不存在则返回None
        """
        return self._protocols.get(protocol_name)

    def add_middleware(self, protocol_name: str, middleware: Callable) -> None:
        """
        为协议添加中间件

        Args:
            protocol_name: 协议名称
            middleware: 中间件函数
        """
        if protocol_name not in self._protocols:
            raise ValueError(f"Protocol '{protocol_name}' not registered")

        self._middleware[protocol_name].append(middleware)
        logger.info(f"Middleware added to protocol '{protocol_name}'")

    def process(self, protocol_name: str, data: Any) -> Any:
        """
        处理协议数据

        Args:
            protocol_name: 协议名称
            data: 要处理的数据

        Returns:
            Any: 处理后的数据

        Raises:
            ValueError: 协议未注册或数据验证失败
        """
        handler = self.get_handler(protocol_name)
        if not handler:
            raise ValueError(f"Protocol '{protocol_name}' not registered")

        # 验证数据
        if not handler.validate(data):
            raise ValueError(f"Invalid data format for protocol '{protocol_name}'")

        # 执行中间件
        processed_data = data
        for middleware in self._middleware[protocol_name]:
            processed_data = middleware(processed_data)

        # 处理数据
        result = handler.handle(processed_data)
        logger.info(f"Protocol '{protocol_name}' processed data successfully")
        return result

    def list_protocols(self) -> list:
        """
        列出所有已注册的协议

        Returns:
            list: 协议名称列表
        """
        return list(self._protocols.keys())

    def is_registered(self, protocol_name: str) -> bool:
        """
        检查协议是否已注册

        Args:
            protocol_name: 协议名称

        Returns:
            bool: 是否已注册
        """
        return protocol_name in self._protocols


# 示例协议处理器
class HTTPProtocolHandler(ProtocolHandler):
    """HTTP协议处理器"""

    def validate(self, data: Any) -> bool:
        """验证HTTP请求数据"""
        if not isinstance(data, dict):
            return False
        required_fields = ['url', 'method']
        return all(field in data for field in required_fields)

    def handle(self, data: Any) -> Any:
        """处理HTTP请求"""
        url = data['url']
        method = data.get('method', 'GET').upper()
        headers = data.get('headers', {})
        params = data.get('params', {})
        json_data = data.get('json')

        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, params=params)
            elif method == 'POST':
                response = requests.post(url, headers=headers, params=params, json=json_data)
            elif method == 'PUT':
                response = requests.put(url, headers=headers, params=params, json=json_data)
            elif method == 'DELETE':
                response = requests.delete(url, headers=headers, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            return {
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'content': response.text,
                'json': response.json() if response.headers.get('content-type', '').startswith('application/json') else None
            }
        except Exception as e:
            logger.error(f"HTTP request failed: {e}")
            raise


class WebSocketProtocolHandler(ProtocolHandler):
    """WebSocket协议处理器示例"""

    def validate(self, data: Any) -> bool:
        """验证WebSocket数据"""
        if not isinstance(data, dict):
            return False
        return 'url' in data and 'message' in data

    def handle(self, data: Any) -> Any:
        """处理WebSocket消息"""
        # 这里只是示例，实际需要websocket库
        logger.info(f"WebSocket message to {data['url']}: {data['message']}")
        return {"status": "sent", "message": data['message']}


# 全局协议注册器实例
protocol_registry = ProtocolRegistry()

# 注册默认协议
protocol_registry.register('http', HTTPProtocolHandler())
protocol_registry.register('websocket', WebSocketProtocolHandler())


def register_protocol(name: str, handler: ProtocolHandler) -> None:
    """便捷函数：注册协议"""
    protocol_registry.register(name, handler)


def process_protocol(name: str, data: Any) -> Any:
    """便捷函数：处理协议数据"""
    return protocol_registry.process(name, data)


# 使用示例
if __name__ == "__main__":
    # HTTP协议使用示例
    http_data = {
        'url': 'https://httpbin.org/get',
        'method': 'GET',
        'params': {'test': 'value'}
    }

    try:
        result = process_protocol('http', http_data)
        print("HTTP Response:", result['status_code'])
    except Exception as e:
        print(f"Error: {e}")

    # 列出所有协议
    print("Registered protocols:", protocol_registry.list_protocols())